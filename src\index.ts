import express, { Request, Response } from "express";
import axios from "axios";
import { Client, LocalAuth, Message, MessageMedia } from "whatsapp-web.js";
import qrcode from "qrcode-terminal";
import { GoogleGenerativeAI, ChatSession } from "@google/generative-ai";
import 'dotenv/config';

const genAI = new GoogleGenerativeAI(process.env.API_KEY!);

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const port = 5000;

async function mediaToGenerativePart(media: MessageMedia) {
  return {
    inlineData: { data: media.data, mimeType: media.mimetype },
  };
}

const whatsappClient = new Client({
  authStrategy: new LocalAuth({
    dataPath: './wa-session'
  }),
  puppeteer: {
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu'
    ],
  },
});

whatsappClient.on("qr", (qr: string) => {
  qrcode.generate(qr, { small: true });
  console.log("QR Code received, scan with your phone.");
});

whatsappClient.on("ready", () => {
  console.log("WhatsApp Web client is ready!");
});

whatsappClient.on("authenticated", () => {
  console.log("WhatsApp client authenticated successfully!");
});

whatsappClient.on("auth_failure", (msg) => {
  console.error("Authentication failed:", msg);
});

whatsappClient.on("disconnected", (reason) => {
  console.log("WhatsApp client disconnected:", reason);
});

whatsappClient.on("error", (error) => {
  console.error("WhatsApp client error:", error);
});

whatsappClient.on("message", async (msg: Message) => {
  try {
    const senderNumber: string = msg.from;
    const message: string = msg.body;

    console.log(`Received message from ${senderNumber}: ${message}`);

    // Cek apakah pesan dari grup atau private chat
    const chat = await msg.getChat();

    // Hanya balas jika pesan dari private chat (bukan grup) dan dimulai dengan "/"
    if (!chat.isGroup) {
      // Cek apakah pesan dimulai dengan "/"
      if (message.startsWith("/")) {
        // Hapus "/" dari awal pesan untuk dikirim ke AI
        const cleanMessage = message.substring(1).trim();

        // Jika pesan kosong setelah "/", berikan pesan bantuan
        if (!cleanMessage) {
          await sendWhatsAppMessage("Halo! Kirim pesan dengan format: /[pesan Anda]\n\nContoh:\n/Halo, apa kabar?\n/Jelaskan tentang AI\n/Bantu saya dengan coding", senderNumber);
          return;
        }

        let mediaPart = null;

        if (msg.hasMedia) {
          try {
            const media = await msg.downloadMedia();
            mediaPart = await mediaToGenerativePart(media);
          } catch (mediaError) {
            console.error("Error downloading media:", mediaError);
            await sendWhatsAppMessage("Maaf, terjadi error saat memproses media. Silakan coba lagi.", senderNumber);
            return;
          }
        }

        await run(cleanMessage, senderNumber, mediaPart);
      } else {
        console.log(`Ignoring message without "/" prefix from ${senderNumber}`);
      }
    } else {
      console.log(`Ignoring group message from ${chat.name}`);
    }
  } catch (error) {
    console.error("Error in message handler:", error);
  }
});

whatsappClient.initialize();

let chat: ChatSession | null = null;

async function run(message: string, senderNumber: string, mediaPart?: any): Promise<void> {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash-preview-04-17" });

    if (!chat) {
      chat = model.startChat({
        generationConfig: {
          maxOutputTokens: 65536,
        },
      });
    }
    let prompt: any[] = [];

    prompt.push(message);

    if (mediaPart) {
      prompt.push(mediaPart);
    }
    
    const result = await chat.sendMessage(prompt);
    const response = result.response;
    const text: string = response.text();


    if (text) {
      console.log("Generated Text:", text);
      await sendWhatsAppMessage(text, senderNumber);
    } else {
      console.error("This problem is related to Model Limitations and API Rate Limits");
    }

  } catch (error) {
    console.error("Error in run function:", error);
    await sendWhatsAppMessage("Oops, an error occurred. Please try again later.", senderNumber);
  }
}

async function sendWhatsAppMessage(text: string, toNumber: string): Promise<void> {
  try {
    // Pastikan client sudah ready
    if (!whatsappClient.info) {
      console.error("WhatsApp client not ready yet");
      return;
    }

    // Validasi format nomor
    if (!toNumber || toNumber.length < 10) {
      console.error("Invalid phone number format:", toNumber);
      return;
    }

    // Tambahkan delay kecil untuk menghindari rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Kirim pesan dengan retry mechanism
    let retries = 3;
    while (retries > 0) {
      try {
        await whatsappClient.sendMessage(toNumber, text);
        console.log(`Message sent successfully to ${toNumber}`);
        break;
      } catch (sendError) {
        retries--;
        console.error(`Send attempt failed, retries left: ${retries}`, sendError);

        if (retries > 0) {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 2000));
        } else {
          throw sendError;
        }
      }
    }
  } catch (err) {
    console.error("Failed to send WhatsApp message:");
    console.error("Error details:", err);

    // Coba alternatif menggunakan chat object
    try {
      const chat = await whatsappClient.getChatById(toNumber);
      await chat.sendMessage(text);
      console.log(`Message sent via chat object to ${toNumber}`);
    } catch (altError) {
      console.error("Alternative send method also failed:", altError);
    }
  }
}

app.listen(port, () => console.log(`Express app running on port ${port}!`));
